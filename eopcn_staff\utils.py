from azure.storage.blob import generate_blob_sas, BlobSasPermissions
from datetime import datetime, timedelta
import os

def get_blob_sas_url(blob_name):
    # Replace with your Azure Blob Storage details
    account_name = 'eopcnstaffphotos'
    account_key = '****************************************************************************************'
    container_name = 'staffphotos'

    sas_token = generate_blob_sas(
        account_name=account_name,
        account_key=account_key,
        container_name=container_name,
        blob_name=blob_name,
        permission=BlobSasPermissions(read=True),
        expiry=datetime.utcnow() + timedelta(hours=1)  # Set your expiry time
    )

    url = f"https://{account_name}.blob.core.windows.net/{container_name}/{blob_name}?{sas_token}"
    return url

"""
Utility functions for tracking and formatting model field changes
"""

def capture_original_data(instance, excluded_fields=None):
    """
    Capture original data from a model instance before changes
    
    Args:
        instance: Django model instance
        excluded_fields: List of field names to exclude from tracking
        
    Returns:
        dict: Original field values
    """
    if excluded_fields is None:
        excluded_fields = ['date_created', 'date_modified', 'created_by', 'modified_by']
    
    original_data = {}
    for field in instance._meta.fields:
        field_name = field.name
        if field_name not in excluded_fields:
            original_data[field_name] = getattr(instance, field_name)
    
    return original_data

def track_changes(original_data, new_data, field_labels=None):
    """
    Track changes between original and new data
    
    Args:
        original_data: dict of original field values
        new_data: dict of new field values (usually from form.cleaned_data)
        field_labels: dict mapping field names to display labels
        
    Returns:
        dict: Changes with old_value, new_value, and field_label for each changed field
    """
    if field_labels is None:
        field_labels = {}
    
    changes = {}
    
    for field_name, new_value in new_data.items():
        if field_name in original_data:
            old_value = original_data[field_name]
            
            # Handle different field types
            if hasattr(old_value, 'name') and hasattr(new_value, 'name'):
                # File fields - compare filenames
                old_file_name = old_value.name if old_value else None
                new_file_name = new_value.name if new_value else None
                if old_file_name != new_file_name:
                    changes[field_name] = {
                        'field_label': field_labels.get(field_name, field_name.replace('_', ' ').title()),
                        'old_value': old_file_name or 'No file',
                        'new_value': new_file_name or 'No file'
                    }
            else:
                # Regular fields - normalize and compare
                normalized_old = normalize_value(old_value)
                normalized_new = normalize_value(new_value)
                
                # Convert to strings for comparison
                old_str = str(normalized_old) if normalized_old is not None else ''
                new_str = str(normalized_new) if normalized_new is not None else ''
                
                # Only track if actually different
                if old_str != new_str:
                    # Format display values
                    old_display = format_display_value(normalized_old)
                    new_display = format_display_value(normalized_new)
                    
                    changes[field_name] = {
                        'field_label': field_labels.get(field_name, field_name.replace('_', ' ').title()),
                        'old_value': old_display,
                        'new_value': new_display
                    }
    
    return changes

def normalize_value(value):
    """
    Normalize a value for comparison
    
    Args:
        value: The value to normalize
        
    Returns:
        Normalized value
    """
    if value is None:
        return None
    if isinstance(value, str):
        return value.strip() if value.strip() else None
    return value

def format_display_value(value):
    """
    Format a value for display in emails
    
    Args:
        value: The value to format
        
    Returns:
        Formatted string for display
    """
    if value is None:
        return ''
    if isinstance(value, bool):
        return 'Yes' if value else 'No'
    return str(value)

def get_field_labels_for_model(model_name):
    """
    Get default field labels for common models
    
    Args:
        model_name: String name of the model ('staff', 'assignment', 'leave', 'allocation')
        
    Returns:
        dict: Field labels mapping
    """
    field_label_maps = {
        'staff': {
            'first_name': 'First Name',
            'last_name': 'Last Name', 
            'start_date': 'Start Date',
            'suggested_email': 'Suggested Email',
            'end_date': 'End Date',
            'currently_active': 'Currently Active',
            'n95_mask_size': 'N95 Mask Size',
            'office_number': 'Office Number',
            'desk_number': 'Desk Number',
            'phone': 'Phone',
            'ext': 'Extension',
            'computer_number': 'Computer Number',
            'photo': 'Photo',
            'comment': 'Comment'
        },
        'assignment': {
            'role': 'Role',
            'supervisor': 'Supervisor', 
            'position': 'Position',
            'permanent_vs_temporary': 'Assignment Type',
            'start_date': 'Start Date',
            'end_date': 'End Date',
            'role_fte': 'FTE'
        },
        'leave': {
            'leave_type': 'Leave Type',
            'leave_start_date': 'Start Date',
            'return_date': 'Return Date',
            'comment': 'Comment'
        },
        'allocation': {
            'clinic': 'Clinic',
            'program': 'Program',
            'fte': 'FTE',
            'start_date': 'Start Date',
            'end_date': 'End Date',
            'currently_active': 'Currently Active',
            'assignment_in_clinic': 'Assignment in Clinic',
            'monday': 'Monday',
            'tuesday': 'Tuesday',
            'wednesday': 'Wednesday',
            'thursday': 'Thursday',
            'friday': 'Friday'
        },
        'physician': {
            'physician_name': 'Physician Name',
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'title': 'Title',
            'practitioner_id': 'Practitioner ID',
            'gender': 'Gender',
            'primary_email': 'Primary Email',
            'primary_phone': 'Primary Phone',
            'alternate_email': 'Alternate Email',
            'alternate_phone': 'Alternate Phone',
            'do_not_email': 'Email Opt-out',
            'date_signed_eopcn': 'Date Signed (EOPCN)',
            'date_left_eopcn': 'Date Left (EOPCN)',
            'comment': 'Comment'
        },
        'clinic': {
            'clinic_name': 'Clinic Name',
            'med_group_or_site': 'Medical Group/Site',
            'street_address': 'Street Address',
            'floor_unit_room': 'Floor/Unit/Room',
            'city': 'City',
            'province': 'Province',
            'postal_code': 'Postal Code',
            'business_phone': 'Phone',
            'extension': 'Extension',
            'fax': 'Fax',
            'clinic_website': 'Website',
            'clinic_emr': 'EMR System',
            'pia_number': 'PIA Number',
            'include_on_eopcn_website': 'Include on EOPCN Website',
            'primary_contact_first_name': 'Primary Contact First Name',
            'primary_contact_last_name': 'Primary Contact Last Name',
            'primary_contact_role': 'Primary Contact Role',
            'primary_contact_phone': 'Primary Contact Phone',
            'primary_contact_ext': 'Primary Contact Ext',
            'primary_contact_email': 'Primary Contact Email',
            'alternate_contact_first_name': 'Alternate Contact First Name',
            'alternate_contact_last_name': 'Alternate Contact Last Name',
            'alternate_contact_role': 'Alternate Contact Role',
            'alternate_contact_phone': 'Alternate Contact Phone',
            'alternate_contact_ext': 'Alternate Contact Ext',
            'alternate_contact_email': 'Alternate Contact Email',
            'comment': 'Comment'
        }
    }
    
    return field_label_maps.get(model_name, {})

def track_formset_changes(original_data_list, formset, field_labels=None):
    """
    Track changes in a formset (for allocations)
    
    Args:
        original_data_list: list of dicts with original data for each form
        formset: Django formset with cleaned_data
        field_labels: dict mapping field names to display labels
        
    Returns:
        list: Changes for each form in the formset
    """
    if field_labels is None:
        field_labels = {}
    
    formset_changes = []
    
    for i, form in enumerate(formset):
        if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
            # Get original data for this form (if it exists)
            original_data = original_data_list[i] if i < len(original_data_list) else {}
            
            # Track changes for this form
            form_changes = track_changes(original_data, form.cleaned_data, field_labels)
            
            # Add allocation identifier for context
            allocation_name = f"Allocation {i+1}"
            if form.cleaned_data.get('clinic'):
                # Access the clinic name properly
                clinic = form.cleaned_data['clinic']
                if hasattr(clinic, 'clinic_name'):
                    allocation_name = f"{clinic.clinic_name}"
                else:
                    allocation_name = f"{clinic}"
            elif form.cleaned_data.get('program'):
                # Access the program name properly  
                program = form.cleaned_data['program']
                if hasattr(program, 'program_name'):
                    allocation_name = f"{program.program_name}"
                else:
                    allocation_name = f"{program}"
            
            if form_changes:
                formset_changes.append({
                    'allocation_name': allocation_name,
                    'changes': form_changes
                })
    
    return formset_changes
