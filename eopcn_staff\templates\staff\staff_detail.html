{% extends "base.html" %}

{% block title %}Staff Detail{% endblock %}

{% block content %}
{% load custom_filters %}
<!DOCTYPE html>
<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">

<style>
  html {
    box-sizing: border-box;
  }
  
  *, *:before, *:after {
    box-sizing: inherit;
  }
  
  .row {
    display: flex;
  }
  
  .column {
    padding: 0 8px;
  }
  
  .left-column {
    flex: 0 0 33.3%; /* Fixed width for left column */
  }
  
  .right-columns {
    flex: 1; /* Remaining width for right columns */
    display: flex;
    flex-direction: column;
  }
  
  .assignment-row {
    display: flex;
    margin-bottom: 16px; /* Space between assignment rows */
  }
  
  .assignment-column {
    flex: 0 0 50%; /* Half of the right side */
    padding: 0 8px;
  }
  
  .allocation-column {
    flex: 1; /* Remaining space */
    padding: 0 8px;
  }
  
  .card {
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
    transition: 0.3s;
    border-radius: 5px;
  }
  
  .card:hover {
    box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
  }
  
  .container {
    padding: 16px;
  }
  
  .title {
    color: grey;
  }
  
  .edit-button {
    display: inline-block;
    padding: 10px 20px;
    margin: 10px 5px;
    font-size: 16px;
    border-radius: 4px;
    background-color: #80caf0;
    color: white;
    text-align: center;
    text-decoration: none;
  }
  
  .edit-button:hover {
    background-color: #015081;
  }
  
  .weekday-row {
    display: flex;
    gap: 30px;
    justify-content: flex-start;
  }
  
  .weekday-row p {
    margin: 0;
  }
  
  .active {
    background-color: green;
    color: white;
    padding: 2px 4px;
    border-radius: 4px;
  }
  
  .inactive {
    background-color: red;
    color: white;
    padding: 2px 4px;
    border-radius: 4px;
  }
  
  .hr-assignments {
    width: 100%;
    border: none;
    border-top: 1px solid #989898;
    margin-top: 16px;
    margin-bottom: 16px;
  }

  .green-button {
    background-color: #5dbea3;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    text-decoration: none;  /* Removes underline */
    display: inline-block;   /* Makes the link look like a button */
  }

  .green-button:hover {
      background-color: darkgreen;  /* Optional: Darken the button on hover */
  }

  .button {
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 4px;
  }
  
  .button1 {
      background-color: #008CBA;
  }

  .button-and-dropdown {
    display: flex;
    align-items: center;
}

  .button-and-dropdown .button1 {
      margin-right: 10px; /* Adjust spacing as needed */
  }

  .button-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.left-buttons {
    flex: 0 0 auto;
}

.center-dropdown {
    flex: 1;
    display: flex;
    justify-content: center;
}

.right-buttons {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
}

.right-buttons .button {
  margin-left: 5px;
  padding: 10px 20px;
  background-color: #0067b1;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.right-buttons .button:hover {
  background-color: #0056b3;
}

.button-supervisor {
    background-color: #708090 !important; /* Slate Grey color with !important to override edit-button */
    color: white;
    margin: 0 8px;
    font-weight: bold;
  }
  
  .button-supervisor:hover {
    background-color: #4E5964 !important; /* Darker slate grey on hover with !important to override edit-button:hover */
  }
  
  </style>
  
</head>
<body>

<h2>Staff Details</h2>
{% if messages %}
        <script>
            {% for message in messages %}
                alert("{{ message }}");
            {% endfor %}
        </script>
{% endif %}

<div class="button-container">
    <!-- Left-aligned button -->
    <div class="left-buttons">
        {% comment %} <a href="{% url 'add_staff_form' %}" class="button button1">+ Add New Staff</a>{% endcomment %}
        <a href="{% url 'lists' %}" class="button button1">Staff Lists</a> 
    </div>

    <!-- Centered dropdown -->
    <div class="center-dropdown">
        <form method="get" id="staffDetailForm" style="display: inline;">
            <label for="staff_member" class="sr-only" style="font-size: 1.10em;">Jump to Staff Member:</label>
            <select name="staff_member" id="staff_member" onchange="redirectToStaffDetail()" style="font-size: 1.05em;">
                <option value="" selected disabled>--Select Staff Member--</option>
                {% for staff in staff_members %}
                    <option value="{{ staff.pk }}">{{ staff.first_name }} {{ staff.last_name }}</option>
                {% endfor %}
            </select>
        </form>
    </div>

    <!-- Right-aligned buttons -->    <div class="right-buttons">
        {% if previous_staff %}
            <a href="{% url 'staff_detail' previous_staff.pk %}" class="button"><</a>
        {% endif %}
        
        {% if next_staff %}
            <a href="{% url 'staff_detail' next_staff.pk %}" class="button">></a>
        {% endif %}
    </div>
</div>

<script>
    function redirectToStaffDetail() {
        const staffId = document.getElementById('staff_member').value;
        if (staffId) {
            const url = `{% url 'staff_detail' 0 %}`.replace('0', staffId);
            window.location.href = url;
        }
    }
</script>


  <div class="row">
    <!-- Left Column -->
    <div class="column left-column">
      <!-- Staff Info Card -->
      <div class="card">
        <div class="container" style="display: flex; align-items: flex-start;">
          <!-- Photo -->
          <div style="margin-right: 20px;">
            {% if staff_photo_url %}
              <img src="{{ staff_photo_url }}" alt="{{ staff_member.name }}" style="max-width: 200px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);">
            {% else %}
              <img src="staff_photos/Blank-team-member-photo-800x800_R1RZez6.jpg" alt="{{ staff_member.name }}" style="max-width: 200px;">
            {% endif %}
          </div>
          <!-- Staff Info -->
          <div style="flex-grow: 1;">            <!-- Title and Edit Button in Flexbox -->
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <h1 style="margin: 0;">{{ staff_member.first_name }} {{ staff_member.last_name }}</h1>
                <div style="display: flex; flex-direction: column; gap: 5px;">
                    <a href="{% url 'edit_profile' staff_member.pk %}" class="edit-button" style="margin-left: 20px;">Edit Profile</a>
                    {% if staff_is_supervisor %}
                        <a href="{% url 'supervisor_staff' supervisor_id %}" class="edit-button button-supervisor" style="margin-left: 20px;">View Supervisor Team</a>
                    {% endif %}
                </div>
            </div>
            <p class="title">Database ID: {{ staff_member.staff_id }}</p>
            <p>Currently Active:
                <span class="{% if staff_member.currently_active %}active{% else %}inactive{% endif %}">
                    {{ staff_member.currently_active|yesno:"Yes,No" }}
                </span>
            </p>
            <p>Start Date: {{ staff_member.start_date }}</p>
            {% if staff_member.end_date %}
                <p>End Date: {{ staff_member.end_date }}</p>
            {% endif %}
            <p>Email: {{ staff_member.suggested_email }}</p>
            <p>N95 Mask Size: {{ staff_member.n95_mask_size }}</p>
            {% comment %} {% if staff_member.office_number %}
              <p>Office/Cubicle: {{ staff_member.office_number }}</p>
            {% endif %}
            {% if staff_member.desk_number %}
              <p>Desk number: {{ staff_member.desk_number }}</p>
            {% endif %}
            <p>Office phone: {{ staff_member.phone }}</p>
            <p>Ext: {{ staff_member.ext }}</p> {% endcomment %}
            <p>Computer Number: {{ staff_member.computer_number }}</p>
          </div>
        </div>
      </div>

      <!-- Staff Location Contact Card -->      <div class="card">
        <div class="container">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2>Contact Information</h2>
            <div>
              <a href="{% url 'seating_map' %}">EOPCN Seating Map</a>
              <a href="{% url 'add_contact_info' staff_member.pk %}" class="edit-button green-button" style="margin-left: 10px;">+ Add Contact Info</a>
            </div>
          </div>
          <ul>
            {% for contact in location_contacts %} 
            <li style="display: flex; padding: 16px 0; margin-bottom: 16px; border-bottom: 1px solid #ddd; align-items: flex-start;">

                <!-- Flex Container for Clinic Name and Other Info -->
                <div style="display: flex; flex: 1;">
                  <!-- Clinic Name as H3 -->
                  <div style="flex: 0 0 150px; margin-right: 16px;">
                    {% if not contact.clinic.clinic_name %}
                      <h3 style="margin: 0;">{{ contact.contact_type }}</h3>
                    {% else %}
                      <h3 style="margin: 0;">
                        {% if contact.clinic.clinic_id %}
                          <a href="{% url 'clinic_detail' contact.clinic.clinic_id %}" style="color: #0067b1; text-decoration: none;">
                            {{ contact.clinic.clinic_name }}
                          </a>
                        {% else %}
                          {{ contact.clinic.clinic_name }}
                        {% endif %}
                      </h3>
                    {% endif %}
                  </div>

                  <!-- Other Information -->
                  <div style="line-height: 1.5;" >
                    <span>
                      {% if contact.office_number %}
                      <strong>Office:</strong> {{ contact.office_number|default:"N/A" }}<br>
                      {% endif %}
                      <strong>Contact Type:</strong> {{ contact.contact_type|default:"N/A" }}<br>
                      {% if contact.desk_number %}
                      <strong>Desk:</strong> {{ contact.desk_number|default:"N/A" }}<br>
                      {% endif %}
                      <strong>Direct Phone:</strong> {{ contact.phone|phone_format|default:"N/A" }}
                      {% comment %} <strong>Ext:</strong> {{ contact.extension|default:"N/A" }} {% endcomment %}
                      {% if contact.clinic.business_phone %}
                        <br><strong>Clinic Contact:</strong> 
                          {{ contact.clinic.business_phone|phone_format|default:"N/A" }}{% if contact.extension %} Ext. {{ contact.extension }}{% endif %}
                      {% endif %}
                    </span>
                    <br>
                    <span>
                      <strong>Days Present:</strong>
                      {% if contact.monday %}Mon {% endif %}
                      {% if contact.tuesday %}Tue {% endif %}
                      {% if contact.wednesday %}Wed {% endif %}
                      {% if contact.thursday %}Thu {% endif %}
                      {% if contact.friday %}Fri {% endif %}
                    </span>
                    <br>
                    {% if contact.contact_notes %}
                    <span><strong>Notes:</strong> {{ contact.contact_notes|default:"None" }}</span>
                    {% endif %}
                  </div>
                </div>
                <a href="{% url 'edit_contact_info' contact.location_contact_id %}" class="edit-button" style="margin-left: auto;">Edit</a>          
              </li>
              {% empty %}
              <li>No contact information available.</li>
              {% endfor %}
          </ul>
        </div>
      </div>

<!-- Leave Records Card -->
<div class="card">  <div class="container">
      <div style="display: flex; justify-content: space-between; align-items: center;">
          <h2>Leave Records</h2>
          <a href="{% url 'add_staff_leave' staff_member.pk %}" class="edit-button green-button">+ Add Leave</a>
      </div>
      <ul>
          {% for leave in staff_member.leaves.all %}
              <li style="display: flex; flex-direction: column; padding: 10px 0;">
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                      <div>
                          <strong>{{ leave.leave_type }}</strong> 
                          from {{ leave.leave_start_date }} to {{ leave.return_date }}
                      </div>
                      <a href="{% url 'edit_staff_leave' leave.pk %}" class="edit-button">Edit</a>
                  </div>

                  <!-- Coverage Records -->
                  <ul style="margin-left: 20px; margin-top: 5px;">
                    {% for coverage in leave.staffcoverage_set.all %}
                        <li>
                            {% if coverage.covering_staff %}
                                Covering Staff:
                                <a href="{% url 'staff_detail' coverage.covering_staff.pk %}">
                                    {{ coverage.covering_staff.first_name }} {{ coverage.covering_staff.last_name }}
                                </a>
                                from {{ coverage.coverage_start_date }} to {{ coverage.coverage_end_date }}
                                {% if coverage.coverage_type %}
                                    <br>Coverage Type: {{ coverage.coverage_type }}
                                    {% else %}
                                    <br>Coverage Type: <em>No coverage type specified</em>
                                {% endif %}
                            {% else %}
                                <em>No covering staff assigned</em>
                            {% endif %}
                        </li>
                    {% empty %}
                        <li><em>No coverage records available for this leave.</em></li>
                    {% endfor %}
                  </ul>
              </li>
          {% empty %}
              <li>No leave records available.</li>
          {% endfor %}
      </ul>
  </div>
</div>
</div>


    <!-- Right Columns -->
    <div class="right-columns">
      <!-- Loop through Assignments -->
      {% for assignment in assignments %}
        <!-- Horizontal line separating assignments -->
        {% if not forloop.first %}
          <hr class="hr-assignments">
        {% endif %}

        <div class="assignment-row">
          <!-- Assignment Column (Column 2) -->          <div class="column assignment-column">
            <div class="card" style="position: relative;">
              {% if forloop.first %}
                <div style="position: absolute; right: 20px; top: 16px;">
                  <a href="{% url 'add_staff_assignment' staff_member.pk %}" class="edit-button green-button">+ Add Role Assignment</a>
                </div>
              {% endif %}
              <div class="container">                <h2 style="display: inline-block;">Role Assignment {{ forloop.counter }}</h2> 
                <div style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%);">
                  <a href="{% url 'edit_assignment' assignment.pk %}" class="edit-button">Edit Role</a>
                </div>
                {% comment %} <a href="{% url 'add_staff_assignment' staff_member.pk %}" class="edit-button green-button" style="float: right">+ Add Role</a> {% endcomment %}

                <h3>Role: {{ assignment.role.role_name }} (FTE: {{ assignment.role_fte }})</h3>

                <p>Currently Active: 
                  <span class="{% if assignment.currently_active %}active{% else %}inactive{% endif %}">
                    {{ assignment.currently_active|yesno:"Yes,No" }}
                  </span>
                </p>                <p>Program: {{ assignment.service.service_name }}</p>
                <p>Position Number: {{ assignment.position.position_number }}</p>
                <p>Perm/temp: {{ assignment.permanent_vs_temporary }}</p>
                <p>Reports To: 
                  {% if assignment.supervisor and assignment.supervisor.pk %}
                    <a href="{% url 'supervisor_staff' assignment.supervisor.pk %}" title="View supervisor's team page">
                      {{ assignment.supervisor.staff.first_name }} {{ assignment.supervisor.staff.last_name }} <i>(view team)</i>
                    </a>
                  {% else %}
                    {{ assignment.supervisor.staff.first_name|default:"" }} {{ assignment.supervisor.staff.last_name|default:"" }}
                  {% endif %}
                </p>
                <p>Start Date: {{ assignment.start_date }}</p>
                <p>End Date: {{ assignment.end_date }}</p>

                <!-- Coverage Information -->
                <ul>
                  {% for coverage in staff_member.coverages.all %}
                  <h3>Covering For:</h3>
                  <li>
                      <a href="{% url 'staff_detail' coverage.leave.staff.pk %}">
                        {{ coverage.leave.staff.first_name }} {{ coverage.leave.staff.last_name }}
                      </a>
                      (Coverage Start: {{ coverage.coverage_start_date }}, Coverage End: {{ coverage.coverage_end_date }})
                    </li>
                  {% empty %}
                  {% endfor %}
                </ul>

              </div>
            </div>
          </div>

          <!-- Allocation Column (Column 3) -->          <div class="column allocation-column">
            <div class="card" style="position: relative;">
              <div class="container">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                  <h2 style="margin: 0;">FTE Allocations for Role {{ forloop.counter }}</h2>
                  <a href="{% url 'edit_allocation' assignment.pk %}" class="edit-button">Add and Edit Allocations</a>
                </div>
                {% for allocation in assignment.allocations.all %}
                  <!-- Allocation Details -->
                  <p>Currently Active: 
                    <span class="{% if allocation.currently_active %}active{% else %}inactive{% endif %}">
                      {{ allocation.currently_active|yesno:"Yes,No" }}
                    </span>
                  </p>
                  <p>Allocation Type: {{ allocation.centralized_vs_ric }}</p>
                  {% if allocation.clinic %}
                    <p>Clinic Name: 
                      {% if allocation.clinic.clinic_id %}
                        <a href="{% url 'clinic_detail' allocation.clinic.clinic_id %}" style="color: #0067b1; text-decoration: none;">
                          {{ allocation.clinic.clinic_name }}
                        </a>
                      {% else %}
                        {{ allocation.clinic.clinic_name }}
                      {% endif %}
                    </p>
                  {% endif %}
                  {% if allocation.assignment_in_clinic %}
                    <p>Assignment in Clinic: {{ allocation.assignment_in_clinic }}</p>
                  {% endif %}
                  {% if allocation.program %}
                    <p>EOPCN Team: {{ allocation.program.program_name }}</p>
                  {% endif %}
                  <p>Allocation Delivery: {{ allocation.allocation_type }}</p>
                  {% if allocation.allocation_type == "Remote" or allocation.allocation_type == "In-Person" %}
                    <p>FTE: {{ allocation.fte }}</p>
                  {% else %}
                  {% endif %}
                  <p>Start Date: {{ allocation.start_date }}</p>
                  {% if allocation.end_date %}
                    <p>End Date: {{ allocation.end_date }}</p>
                  {% endif %}
                  <!-- Display Mon-Fri fields horizontally -->
                  <div class="weekday-row">
                    {% if allocation.allocation_type == "Remote" or allocation.allocation_type == "Project Based Support" %}
                    {% else %}
                      <p>Mon: {% if allocation.monday %}✔{% else %}×{% endif %}</p>
                      <p>Tue: {% if allocation.tuesday %}✔{% else %}×{% endif %}</p>
                      <p>Wed: {% if allocation.wednesday %}✔{% else %}×{% endif %}</p>
                      <p>Thu: {% if allocation.thursday %}✔{% else %}×{% endif %}</p>
                      <p>Fri: {% if allocation.friday %}✔{% else %}×{% endif %}</p>
                    {% endif %}
                  </div>
                  <hr>
                {% empty %}
                  <p>No allocations for this role.</p>
                {% endfor %}
              </div>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>
  </div>

</body>
</html>
{% endblock %}