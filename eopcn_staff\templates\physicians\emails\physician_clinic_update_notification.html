<!DOCTYPE html>
<html>
<head>
    <title>Clinic Associations Updated for {{ physician.physician_name }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        table { width:70%; border-collapse: separate; border-spacing: 2px; }
        td { padding: 5px; }
        .label { text-align: right; width: 15%; }
        .value { background-color: #DDEBF7; width: 55%; }
        .clinic-section { margin-bottom: 20px; }
        .clinic-title { background-color: #f0f0f0; font-weight: bold; padding: 10px; }
    </style>
</head>
<body>
    <h1>Clinic Associations Update</h1>
    <p>This email details the clinic association changes for primary care provider: <strong>{{ physician.last_name }}, {{ physician.first_name }}</strong>.</p>

    <h2>Clinic Associations</h2>
    {% for item in clinic_physicians %}
        {% if item.clinic and item.clinic.clinic_name %}
            <!-- Find changes for this specific clinic association -->
            {% with clinic_changes=None %}
                {% for change_item in changes %}
                    {% if change_item.allocation_name == item.clinic.clinic_name %}
                        {% with clinic_changes=change_item.changes %}

                        <div class="clinic-section">
                            <div class="clinic-title">{{ item.clinic.clinic_name }}</div>
                            <table border="0" cellspacing="2" style="width:70%">
                                <tr>
                                    <td class="label"{% if clinic_changes.clinic %} style="background-color:#fff8c6;"{% endif %}>Clinic Name:</td>
                                    <td class="value">
                                        {% if clinic_changes.clinic %}
                                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ clinic_changes.clinic.old_value }}</span>
                                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ clinic_changes.clinic.new_value }}</span>
                                        {% else %}
                                            {{ item.clinic.clinic_name }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="label"{% if clinic_changes.portion_of_practice %} style="background-color:#fff8c6;"{% endif %}>Portion of Practice:</td>
                                    <td class="value">
                                        {% if clinic_changes.portion_of_practice %}
                                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ clinic_changes.portion_of_practice.old_value }}</span>
                                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ clinic_changes.portion_of_practice.new_value }}</span>
                                        {% else %}
                                            {{ item.portion_of_practice }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="label"{% if clinic_changes.accepting_patients %} style="background-color:#fff8c6;"{% endif %}>Accepting Patients:</td>
                                    <td class="value">
                                        {% if clinic_changes.accepting_patients %}
                                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ clinic_changes.accepting_patients.old_value }}</span>
                                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ clinic_changes.accepting_patients.new_value }}</span>
                                        {% else %}
                                            {{ item.accepting_patients }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="label"{% if clinic_changes.include_on_afad_website %} style="background-color:#fff8c6;"{% endif %}>Include on AFAD:</td>
                                    <td class="value">
                                        {% if clinic_changes.include_on_afad_website %}
                                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ clinic_changes.include_on_afad_website.old_value }}</span>
                                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ clinic_changes.include_on_afad_website.new_value }}</span>
                                        {% else %}
                                            {{ item.include_on_afad_website }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="label"{% if clinic_changes.include_on_eopcn_website %} style="background-color:#fff8c6;"{% endif %}>Include on EOPCN:</td>
                                    <td class="value">
                                        {% if clinic_changes.include_on_eopcn_website %}
                                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ clinic_changes.include_on_eopcn_website.old_value }}</span>
                                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ clinic_changes.include_on_eopcn_website.new_value }}</span>
                                        {% else %}
                                            {{ item.include_on_eopcn_website }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="label"{% if clinic_changes.date_active_in_clinic %} style="background-color:#fff8c6;"{% endif %}>Date Active:</td>
                                    <td class="value">
                                        {% if clinic_changes.date_active_in_clinic %}
                                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ clinic_changes.date_active_in_clinic.old_value }}</span>
                                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ clinic_changes.date_active_in_clinic.new_value }}</span>
                                        {% else %}
                                            {{ item.date_active_in_clinic }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="label"{% if clinic_changes.date_left_clinic %} style="background-color:#fff8c6;"{% endif %}>Date Left:</td>
                                    <td class="value">
                                        {% if clinic_changes.date_left_clinic %}
                                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ clinic_changes.date_left_clinic.old_value }}</span>
                                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ clinic_changes.date_left_clinic.new_value }}</span>
                                        {% else %}
                                            {{ item.date_left_clinic }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="label"{% if clinic_changes.CPAR_Panel_ID %} style="background-color:#fff8c6;"{% endif %}>CPAR Panel ID:</td>
                                    <td class="value">
                                        {% if clinic_changes.CPAR_Panel_ID %}
                                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ clinic_changes.CPAR_Panel_ID.old_value }}</span>
                                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ clinic_changes.CPAR_Panel_ID.new_value }}</span>
                                        {% else %}
                                            {{ item.CPAR_Panel_ID|default:"Not specified" }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="label"{% if clinic_changes.active_CII %} style="background-color:#fff8c6;"{% endif %}>Active CII:</td>
                                    <td class="value">
                                        {% if clinic_changes.active_CII %}
                                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ clinic_changes.active_CII.old_value }}</span>
                                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ clinic_changes.active_CII.new_value }}</span>
                                        {% else %}
                                            {% if item.active_CII %}Yes{% else %}No{% endif %}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="label"{% if clinic_changes.active_CPAR %} style="background-color:#fff8c6;"{% endif %}>Active CPAR:</td>
                                    <td class="value">
                                        {% if clinic_changes.active_CPAR %}
                                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ clinic_changes.active_CPAR.old_value }}</span>
                                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ clinic_changes.active_CPAR.new_value }}</span>
                                        {% else %}
                                            {% if item.active_CPAR %}Yes{% else %}No{% endif %}
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>

                        {% endwith %}
                        {% break %}
                    {% endif %}
                {% empty %}
                    <!-- No changes found for this clinic, show current values -->
                    <div class="clinic-section">
                        <div class="clinic-title">{{ item.clinic.clinic_name }}</div>
                        <table border="0" cellspacing="2" style="width:70%">
                            <tr>
                                <td class="label">Clinic Name:</td>
                                <td class="value">{{ item.clinic.clinic_name }}</td>
                            </tr>
                            <tr>
                                <td class="label">Portion of Practice:</td>
                                <td class="value">{{ item.portion_of_practice }}</td>
                            </tr>
                            <tr>
                                <td class="label">Accepting Patients:</td>
                                <td class="value">{{ item.accepting_patients }}</td>
                            </tr>
                            <tr>
                                <td class="label">Include on AFAD:</td>
                                <td class="value">{{ item.include_on_afad_website }}</td>
                            </tr>
                            <tr>
                                <td class="label">Include on EOPCN:</td>
                                <td class="value">{{ item.include_on_eopcn_website }}</td>
                            </tr>
                            <tr>
                                <td class="label">Date Active:</td>
                                <td class="value">{{ item.date_active_in_clinic }}</td>
                            </tr>
                            <tr>
                                <td class="label">Date Left:</td>
                                <td class="value">{{ item.date_left_clinic }}</td>
                            </tr>
                            <tr>
                                <td class="label">CPAR Panel ID:</td>
                                <td class="value">{{ item.CPAR_Panel_ID|default:"Not specified" }}</td>
                            </tr>
                            <tr>
                                <td class="label">Active CII:</td>
                                <td class="value">{% if item.active_CII %}Yes{% else %}No{% endif %}</td>
                            </tr>
                            <tr>
                                <td class="label">Active CPAR:</td>
                                <td class="value">{% if item.active_CPAR %}Yes{% else %}No{% endif %}</td>
                            </tr>
                        </table>
                    </div>
                {% endfor %}
            {% endwith %}
        {% endif %}
    {% endfor %}

    {% if comment %}
    <h2>Additional Comments</h2>
    <table border="0" cellspacing="2" style="width:70%">
        <tr>
            <td class="label">Comment:</td>
            <td class="value">{{ comment }}</td>
        </tr>
    </table>
    {% endif %}

    <br>
    {% if details_url %}
    <p>
        <a href="{{ details_url }}">View record in operational database</a>
    </p>
    {% endif %}
    <p>
        Submitted by: <strong>{{ user.full_name }}</strong> ({{ user.email }}).
    </p>
    <p>Best regards,</p>
    <p>EOPCN Automation Admin</p>
</body>
</html>